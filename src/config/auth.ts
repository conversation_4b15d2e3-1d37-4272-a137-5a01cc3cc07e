// TODO: Replace this expired token with your valid token that expires in August
export const AUTH_TOKEN =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI3ZjE4MDQwNC1mNmNlLTQwNTMtYmI4YS1jYjU5MmJhMDQxYmQiLCJpYXQiOjE3NDg0MzgwMzMsImV4cCI6MTc0ODUyNDQzMywiYXVkIjoiZmFuIiwianRpIjoiZWQxMTM0N2QtOWZhYy00YWI4LTkxNDAtOWM3YmZlYTc5MDI2In0.7D90QbjhUXw0WjfhThRGBwEmXxAgHJgeSuvBy1o03zo";

// Helper function to decode JWT token expiration (for debugging)
export const getTokenExpiration = (token: string) => {
	try {
		const payload = JSON.parse(atob(token.split(".")[1]));
		const expDate = new Date(payload.exp * 1000);
		return {
			exp: payload.exp,
			expDate: expDate.toISOString(),
			isExpired: Date.now() > payload.exp * 1000,
		};
	} catch (error) {
		return { error: "Invalid token format" };
	}
};

// for production load this from environment variables or a secure configuration service
export const getAuthToken = () => AUTH_TOKEN;
