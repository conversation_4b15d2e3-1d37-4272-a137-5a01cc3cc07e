// app/index.tsx
import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { LogBox } from "react-native";

// Language Context Provider
import { LanguageProvider } from "../contexts/LanguageContext";

// Pages imports
import HomePage from "../pages/HomePage"; // HomePage component using section renderers
import VideoPlayerPage from "../pages/VideoPlayerPage";
import CategoriesPage from "../pages/CategoriesPage";
// Test page #TODO remove after
import _PaginatedList_Func from "../pages/_PaginatedList";
// Components imports (commented on refactoring)
// import HealthStatusKentico from "../components/HealthStatusKentico";

// Page imports
import VideoDetailsPage from "../pages/VideoDetailsPage";
import CompetitionsPage from "../pages/CompetitionsPage";
import NotFoundPage from "../pages/NotFoundPage";
import ClubDetailsPage from "../pages/ClubDetailsPage";

// Ignore specific warnings and errors - added to be able redirect without error banner from axios
LogBox.ignoreLogs([
	"Error fetching competition page data:",
	"AxiosError: Request failed with status code 404",
	"Error: Request failed with status code 404",
]);

// Updated RootStackParamList to match video props
export type RootStackParamList = {
	VideoPlayer: {
		video: {
			videoId: string;
			title: string;
			thumbnail: string;
			description?: string;
			ratio?: string;
			isLive?: boolean;
			startTime?: string;
		};
	};
	VideoDetailsPage: {
		video: {
			videoId: string;
			title: string;
			thumbnail: string;
			description?: string;
			ratio?: string;
			isLive?: boolean;
			startTime?: string;
		};
	};
	HomePage: undefined;
	WelcomePage: undefined;
	HealthStatusKentico: undefined;
	VideoGrid: undefined;
	NewVideoPlayerPage: undefined;
	SuggestedVideos: undefined;
	Categories:
		| undefined
		| {
				categoryId?: string;
				categoryName?: string;
				categoryImage?: string;
		  };
	CompetitionsPage:
		| undefined
		| {
				pageCodename?: string; // Kentico page codename for the competition
		  };
	ClubDetailsPage:
		| undefined
		| {
				clubId: string; // Club ID for API endpoint construction
				clubName: string; // Club name for display
				clubImage?: string; // Club image for header banner
				clubCodename?: string; // Club codename for direct API access
		  };
	NotFoundPage: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const App = () => (
	<LanguageProvider>
		<NavigationContainer>
			<Stack.Navigator
				screenOptions={{ headerShown: false }} // removes header from tv's (not the same design on different platforms)
				initialRouteName="HomePage" // Set this page as the initial route
			>
				{/* // Current app pages */}
				<Stack.Screen
					name="HomePage"
					component={HomePage}
				/>
				<Stack.Screen
					name="VideoDetailsPage"
					component={VideoDetailsPage}
				/>
				<Stack.Screen
					// statusBarAnimation="slide"
					name="VideoPlayer"
					component={VideoPlayerPage}
				/>
				<Stack.Screen
					name="NotFoundPage"
					component={NotFoundPage}
				/>
				{/* Health Status component (commented on refactoring) */}
				{/* <Stack.Screen
					name="HealthStatusKentico"
					component={HealthStatusKentico}
				/> */}

				<Stack.Screen
					name="Categories"
					component={CategoriesPage}
				/>
				<Stack.Screen
					name="CompetitionsPage"
					component={CompetitionsPage}
				/>
				<Stack.Screen
					name="ClubDetailsPage"
					component={ClubDetailsPage}
				/>
				{/* Testing pagination page */}
				<Stack.Screen
					name="_PaginatedList_Name"
					component={_PaginatedList_Func}
				/>
				{/* #TODO to delete - test route */}
				{/* <Stack.Screen
					name="NotFoundPage"
					component={NotFoundPage}
				/> */}
				{/* #TODO to delete - test route */}
			</Stack.Navigator>
		</NavigationContainer>
	</LanguageProvider>
);

export default App;
